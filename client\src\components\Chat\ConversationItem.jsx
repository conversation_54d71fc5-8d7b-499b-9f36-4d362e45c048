import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import './ConversationItem.css';

const ConversationItem = ({ conversation, isActive, collapsed, onClick }) => {
    const { user } = useAuth();

    const getConversationName = () => {
        if (conversation.type === 'direct') {
            // For direct conversations, show the other user's name
            if (conversation.created_by_username && conversation.created_by_username !== user.username) {
                return conversation.first_name && conversation.last_name 
                    ? `${conversation.first_name} ${conversation.last_name}`
                    : conversation.created_by_username;
            }
            return 'Direct Chat';
        }
        return conversation.name || 'Group Chat';
    };

    const getConversationAvatar = () => {
        if (conversation.avatar_url) {
            return conversation.avatar_url;
        }
        return null;
    };

    const getInitials = (name) => {
        return name
            .split(' ')
            .map(word => word[0])
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    const formatTime = (timestamp) => {
        if (!timestamp) return '';
        
        const date = new Date(timestamp);
        const now = new Date();
        const diffInHours = (now - date) / (1000 * 60 * 60);
        
        if (diffInHours < 24) {
            return date.toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit',
                hour12: false 
            });
        } else if (diffInHours < 24 * 7) {
            return date.toLocaleDateString('en-US', { weekday: 'short' });
        } else {
            return date.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric' 
            });
        }
    };

    const conversationName = getConversationName();
    const avatarUrl = getConversationAvatar();

    return (
        <div 
            className={`conversation-item ${isActive ? 'active' : ''} ${collapsed ? 'collapsed' : ''}`}
            onClick={onClick}
        >
            <div className="conversation-avatar">
                {avatarUrl ? (
                    <img src={avatarUrl} alt="Avatar" />
                ) : (
                    <div className="avatar-placeholder">
                        <span className="avatar-initials">
                            {getInitials(conversationName)}
                        </span>
                    </div>
                )}
                {conversation.type === 'group' && (
                    <div className="conversation-type-indicator">👥</div>
                )}
            </div>

            {!collapsed && (
                <div className="conversation-content">
                    <div className="conversation-header">
                        <div className="conversation-name">
                            {conversationName}
                        </div>
                        <div className="conversation-time">
                            {formatTime(conversation.last_message_at)}
                        </div>
                    </div>
                    
                    <div className="conversation-preview">
                        <div className="last-message">
                            {conversation.last_message_content || 'No messages yet'}
                        </div>
                        {conversation.unread_count > 0 && (
                            <div className="unread-badge">
                                {conversation.unread_count > 99 ? '99+' : conversation.unread_count}
                            </div>
                        )}
                    </div>
                </div>
            )}

            {collapsed && conversation.unread_count > 0 && (
                <div className="unread-dot"></div>
            )}
        </div>
    );
};

export default ConversationItem;
