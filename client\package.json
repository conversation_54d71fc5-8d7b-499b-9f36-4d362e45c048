{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 5173", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.9.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}