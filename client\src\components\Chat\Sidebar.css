.sidebar {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: white;
    transition: all 0.3s ease;
}

.sidebar.collapsed {
    width: 80px;
}

/* Header */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--tg-border-color);
    background: var(--tg-card-bg);
    min-height: 60px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    flex: 1;
    min-width: 0;
    transition: all 0.2s ease;
}

.user-info:hover {
    opacity: 0.8;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--tg-button-color);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;
    transition: all 0.2s ease;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-initials {
    color: white;
    font-weight: 600;
    font-size: 16px;
}

.user-details {
    min-width: 0;
    flex: 1;
}

.user-name {
    font-weight: 500;
    color: var(--tg-text-color);
    font-size: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-status {
    font-size: 13px;
    color: var(--tg-hint-color);
    margin-top: 1px;
}

.header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: all 0.2s ease;
    color: var(--tg-hint-color);
}

.action-btn:hover {
    background: var(--tg-secondary-bg);
    color: var(--tg-text-color);
}

/* Search */
.sidebar-search {
    padding: 12px 16px;
    border-bottom: 1px solid var(--tg-border-color);
    background: var(--tg-card-bg);
}

/* Content */
.sidebar-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

.sidebar-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 40px 20px;
    color: var(--tg-hint-color);
    font-size: 14px;
}

.loading-spinner.small {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0088cc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Footer */
.sidebar-footer {
    padding: 16px;
    border-top: 1px solid var(--tg-border-color);
    background: var(--tg-card-bg);
}

.logout-btn {
    width: 100%;
    padding: 12px 16px;
    border: none;
    background: var(--tg-destructive);
    color: white;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: background-color 0.2s ease;
}

.logout-btn:hover {
    background: #c82333;
}

.logout-icon {
    font-size: 16px;
}

/* Collapsed state adjustments */
.sidebar.collapsed .user-details,
.sidebar.collapsed .sidebar-search,
.sidebar.collapsed .sidebar-footer {
    display: none;
}

.sidebar.collapsed .sidebar-header {
    justify-content: center;
    padding: 16px 8px;
}

.sidebar.collapsed .user-info {
    justify-content: center;
}

.sidebar.collapsed .header-actions {
    position: absolute;
    top: 16px;
    right: 8px;
}

/* Scrollbar styling */
.sidebar-content::-webkit-scrollbar {
    width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}