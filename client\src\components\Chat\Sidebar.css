.sidebar {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: white;
    transition: all 0.3s ease;
}

.sidebar.collapsed {
    width: 80px;
}

/* Header */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.8));
    backdrop-filter: blur(10px);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    flex: 1;
    min-width: 0;
    transition: all 0.2s ease;
}

.user-info:hover {
    opacity: 0.8;
}

.user-avatar {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: linear-gradient(135deg, #0088cc, #005fa3);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 136, 204, 0.3);
    transition: all 0.3s ease;
}

.user-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 136, 204, 0.4);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-initials {
    color: white;
    font-weight: 600;
    font-size: 16px;
}

.user-details {
    min-width: 0;
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: #333;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-status {
    font-size: 12px;
    color: #28a745;
    margin-top: 2px;
}

.header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
    background: rgba(0, 136, 204, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 136, 204, 0.2);
}

/* Search */
.sidebar-search {
    padding: 16px;
    border-bottom: 1px solid #e1e5e9;
}

/* Content */
.sidebar-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

.sidebar-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 40px 20px;
    color: #666;
    font-size: 14px;
}

.loading-spinner.small {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0088cc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Footer */
.sidebar-footer {
    padding: 16px;
    border-top: 1px solid #e1e5e9;
    background: #f8f9fa;
}

.logout-btn {
    width: 100%;
    padding: 12px 16px;
    border: none;
    background: #dc3545;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: background-color 0.2s ease;
}

.logout-btn:hover {
    background: #c82333;
}

.logout-icon {
    font-size: 16px;
}

/* Collapsed state adjustments */
.sidebar.collapsed .user-details,
.sidebar.collapsed .sidebar-search,
.sidebar.collapsed .sidebar-footer {
    display: none;
}

.sidebar.collapsed .sidebar-header {
    justify-content: center;
    padding: 16px 8px;
}

.sidebar.collapsed .user-info {
    justify-content: center;
}

.sidebar.collapsed .header-actions {
    position: absolute;
    top: 16px;
    right: 8px;
}

/* Scrollbar styling */
.sidebar-content::-webkit-scrollbar {
    width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}