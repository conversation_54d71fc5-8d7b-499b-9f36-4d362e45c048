.conversation-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    margin: 0 8px;
    border-radius: 12px;
    margin-bottom: 2px;
}

.conversation-item:hover {
    background: rgba(0, 136, 204, 0.05);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 136, 204, 0.1);
}

.conversation-item.active {
    background: linear-gradient(135deg, rgba(0, 136, 204, 0.1), rgba(0, 95, 163, 0.08));
    border-right: none;
    box-shadow:
        0 4px 12px rgba(0, 136, 204, 0.15),
        inset 3px 0 0 #0088cc;
}

.conversation-item.collapsed {
    justify-content: center;
    padding: 12px 8px;
}

.conversation-avatar {
    position: relative;
    width: 52px;
    height: 52px;
    flex-shrink: 0;
    margin-right: 16px;
}

.conversation-item.collapsed .conversation-avatar {
    margin-right: 0;
}

.conversation-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, #0088cc, #005fa3);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 10px rgba(0, 136, 204, 0.3);
    transition: all 0.3s ease;
}

.conversation-item:hover .avatar-placeholder {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 136, 204, 0.4);
}

.avatar-initials {
    color: white;
    font-weight: 600;
    font-size: 18px;
}

.conversation-type-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.conversation-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

.conversation-name {
    font-weight: 600;
    color: #333;
    font-size: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.conversation-time {
    font-size: 12px;
    color: #666;
    flex-shrink: 0;
}

.conversation-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

.last-message {
    font-size: 13px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.unread-badge {
    background: #0088cc;
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.unread-dot {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: #0088cc;
    border-radius: 50%;
}

/* Active state adjustments */
.conversation-item.active .conversation-name {
    color: #0088cc;
}

.conversation-item.active .conversation-time {
    color: #0088cc;
}

/* Collapsed state adjustments */
.conversation-item.collapsed .conversation-content {
    display: none;
}