.conversation-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f0f2f5;
    transition: background-color 0.2s ease;
    position: relative;
}

.conversation-item:hover {
    background: #f8f9fa;
}

.conversation-item.active {
    background: #e3f2fd;
    border-right: 3px solid #0088cc;
}

.conversation-item.collapsed {
    justify-content: center;
    padding: 12px 8px;
}

.conversation-avatar {
    position: relative;
    width: 48px;
    height: 48px;
    flex-shrink: 0;
    margin-right: 12px;
}

.conversation-item.collapsed .conversation-avatar {
    margin-right: 0;
}

.conversation-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, #0088cc, #0066aa);
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-initials {
    color: white;
    font-weight: 600;
    font-size: 18px;
}

.conversation-type-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.conversation-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

.conversation-name {
    font-weight: 600;
    color: #333;
    font-size: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.conversation-time {
    font-size: 12px;
    color: #666;
    flex-shrink: 0;
}

.conversation-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

.last-message {
    font-size: 13px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.unread-badge {
    background: #0088cc;
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.unread-dot {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: #0088cc;
    border-radius: 50%;
}

/* Active state adjustments */
.conversation-item.active .conversation-name {
    color: #0088cc;
}

.conversation-item.active .conversation-time {
    color: #0088cc;
}

/* Collapsed state adjustments */
.conversation-item.collapsed .conversation-content {
    display: none;
}
