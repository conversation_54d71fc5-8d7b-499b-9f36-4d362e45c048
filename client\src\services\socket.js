import { io } from 'socket.io-client';

class SocketService {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.listeners = new Map();
    }

    connect(token) {
        const SOCKET_URL = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3000';
        
        this.socket = io(SOCKET_URL, {
            transports: ['websocket', 'polling'],
            timeout: 20000,
        });

        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.isConnected = true;
            
            // Authenticate with token
            this.socket.emit('authenticate', { token });
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.isConnected = false;
        });

        this.socket.on('authenticated', (data) => {
            console.log('Authenticated:', data.user);
            this.emit('authenticated', data);
        });

        this.socket.on('auth_error', (data) => {
            console.error('Authentication error:', data.error);
            this.emit('auth_error', data);
        });

        this.socket.on('new_message', (message) => {
            console.log('New message received:', message);
            this.emit('new_message', message);
        });

        this.socket.on('user_typing', (data) => {
            this.emit('user_typing', data);
        });

        this.socket.on('user_status_changed', (data) => {
            this.emit('user_status_changed', data);
        });

        this.socket.on('message_read', (data) => {
            this.emit('message_read', data);
        });

        this.socket.on('error', (data) => {
            console.error('Socket error:', data);
            this.emit('error', data);
        });

        return this.socket;
    }

    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
            this.isConnected = false;
        }
    }

    // Event listener management
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => callback(data));
        }
    }

    // Message methods
    sendMessage(conversationId, content, messageType = 'text', replyToMessageId = null) {
        if (this.socket && this.isConnected) {
            this.socket.emit('send_message', {
                conversation_id: conversationId,
                content,
                message_type: messageType,
                reply_to_message_id: replyToMessageId
            });
        }
    }

    // Typing indicators
    startTyping(conversationId) {
        if (this.socket && this.isConnected) {
            this.socket.emit('typing_start', { conversation_id: conversationId });
        }
    }

    stopTyping(conversationId) {
        if (this.socket && this.isConnected) {
            this.socket.emit('typing_stop', { conversation_id: conversationId });
        }
    }

    // Conversation methods
    joinConversation(conversationId) {
        if (this.socket && this.isConnected) {
            this.socket.emit('join_conversation', { conversation_id: conversationId });
        }
    }

    leaveConversation(conversationId) {
        if (this.socket && this.isConnected) {
            this.socket.emit('leave_conversation', { conversation_id: conversationId });
        }
    }

    // Read receipts
    markAsRead(conversationId, messageId) {
        if (this.socket && this.isConnected) {
            this.socket.emit('mark_as_read', {
                conversation_id: conversationId,
                message_id: messageId
            });
        }
    }
}

// Create singleton instance
const socketService = new SocketService();
export default socketService;
