.search-bar {
    position: relative;
    display: flex;
    align-items: center;
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 24px;
    padding: 12px 20px;
    gap: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(255, 255, 255, 0.2);
}

.search-icon {
    color: #666;
    font-size: 14px;
    flex-shrink: 0;
}

.search-bar:focus-within {
    background: rgba(232, 240, 254, 0.9);
    box-shadow:
        0 4px 20px rgba(0, 136, 204, 0.15),
        0 0 0 2px rgba(0, 136, 204, 0.2);
    transform: translateY(-1px);
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    outline: none;
    font-size: 15px;
    color: #333;
    padding: 6px 0;
    font-weight: 400;
}

.search-input::placeholder {
    color: #94a3b8;
}

.clear-search {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 2px;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: background-color 0.2s ease;
}

.clear-search:hover {
    background: rgba(0, 0, 0, 0.1);
}