.message-item {
    display: flex;
    gap: 8px;
    margin-bottom: 2px;
    align-items: flex-end;
}

.message-item.own {
    flex-direction: row-reverse;
}

.message-item.first-in-group {
    margin-top: 8px;
}

.message-item.last-in-group {
    margin-bottom: 8px;
}

.message-avatar {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
    margin-bottom: 4px;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, #0088cc, #0066aa);
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-initials {
    color: white;
    font-weight: 600;
    font-size: 12px;
}

.message-content {
    max-width: 70%;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.message-item.own .message-content {
    align-items: flex-end;
}

.message-sender {
    font-size: 12px;
    font-weight: 600;
    color: #0088cc;
    margin-bottom: 2px;
    padding: 0 12px;
}

.message-bubble {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 12px 16px;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    position: relative;
    word-wrap: break-word;
    max-width: 100%;
    transition: all 0.2s ease;
}

.message-bubble:hover {
    transform: translateY(-1px);
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.3);
}

.message-item.own .message-bubble {
    background: linear-gradient(135deg, #0088cc, #005fa3);
    color: white;
    box-shadow:
        0 2px 8px rgba(0, 136, 204, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
}

.message-item.own .message-bubble:hover {
    box-shadow:
        0 4px 12px rgba(0, 136, 204, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2);
}

.message-item.first-in-group:not(.own) .message-bubble {
    border-bottom-left-radius: 4px;
}

.message-item.first-in-group.own .message-bubble {
    border-bottom-right-radius: 4px;
}

.message-item:not(.first-in-group):not(.last-in-group) .message-bubble {
    border-radius: 18px;
}

.message-item:not(.first-in-group):not(.last-in-group):not(.own) .message-bubble {
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px;
}

.message-item:not(.first-in-group):not(.last-in-group).own .message-bubble {
    border-bottom-right-radius: 4px;
    border-top-right-radius: 4px;
}

.message-item.last-in-group:not(.own) .message-bubble {
    border-top-left-radius: 4px;
}

.message-item.last-in-group.own .message-bubble {
    border-top-right-radius: 4px;
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 4px;
}

.message-meta {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 4px;
    margin-top: 2px;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
}

.message-item.own .message-time {
    color: rgba(255, 255, 255, 0.8);
}

.message-item:not(.own) .message-time {
    color: #666;
}

.message-status {
    font-size: 12px;
    opacity: 0.8;
    color: rgba(255, 255, 255, 0.8);
}

.message-status.read {
    color: #4fc3f7;
}

/* Hide avatar space for non-first messages in group */
.message-item:not(.first-in-group):not(.own) {
    margin-left: 40px;
}

/* Responsive design */
@media (max-width: 768px) {
    .message-content {
        max-width: 85%;
    }

    .message-text {
        font-size: 13px;
    }

    .message-time {
        font-size: 10px;
    }
}