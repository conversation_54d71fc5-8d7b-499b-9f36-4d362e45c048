.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #0088cc 0%, #005fa3 50%, #003d6b 100%);
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.04)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    padding: 48px;
    width: 100%;
    max-width: 480px;
    animation: slideUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    position: relative;
    z-index: 1;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h1 {
    color: #1a1a1a;
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 12px 0;
    background: linear-gradient(135deg, #0088cc, #005fa3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-header p {
    color: #666;
    font-size: 17px;
    margin: 0;
    font-weight: 400;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: #333;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 6px;
}

.form-group input {
    padding: 16px 20px;
    border: 2px solid #e8ecf0;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    font-weight: 400;
}

.form-group input:focus {
    outline: none;
    border-color: #0088cc;
    box-shadow:
        0 0 0 4px rgba(0, 136, 204, 0.1),
        0 4px 12px rgba(0, 136, 204, 0.15);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
}

.form-group input:disabled {
    background: #f5f5f5;
    cursor: not-allowed;
}

.form-group input::placeholder {
    color: #999;
}

.auth-button {
    background: linear-gradient(135deg, #0088cc 0%, #005fa3 100%);
    color: white;
    border: none;
    padding: 18px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: 16px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 136, 204, 0.3);
}

.auth-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.auth-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 136, 204, 0.4);
    background: linear-gradient(135deg, #0099dd 0%, #0066aa 100%);
}

.auth-button:hover:not(:disabled)::before {
    left: 100%;
}

.auth-button:active:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 136, 204, 0.3);
}

.auth-button:disabled {
    background: linear-gradient(135deg, #ccc 0%, #999 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.error-message {
    background: #fee;
    color: #c33;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #fcc;
    font-size: 14px;
    text-align: center;
}

.auth-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.auth-footer p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.auth-link {
    color: #0088cc;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.auth-link:hover {
    color: #0066aa;
    text-decoration: underline;
}

/* Responsive design */
@media (max-width: 480px) {
    .auth-container {
        padding: 10px;
    }

    .auth-card {
        padding: 30px 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .auth-header h1 {
        font-size: 24px;
    }
}