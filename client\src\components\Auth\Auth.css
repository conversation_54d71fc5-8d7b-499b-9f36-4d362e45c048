/* CSS Variables for theming */
:root {
    --tg-bg-color: #ffffff;
    --tg-secondary-bg: #f4f4f5;
    --tg-text-color: #000000;
    --tg-hint-color: #707579;
    --tg-link-color: #2481cc;
    --tg-button-color: #40a7e3;
    --tg-button-text: #ffffff;
    --tg-destructive: #e53e3e;
    --tg-border-color: #c8c7cc;
    --tg-input-bg: #ffffff;
    --tg-card-bg: #ffffff;
    --tg-shadow: rgba(0, 0, 0, 0.12);
}

[data-theme="dark"] {
    --tg-bg-color: #212121;
    --tg-secondary-bg: #181818;
    --tg-text-color: #ffffff;
    --tg-hint-color: #aaaaaa;
    --tg-link-color: #6ab7ff;
    --tg-button-color: #5288c1;
    --tg-button-text: #ffffff;
    --tg-destructive: #ff6b6b;
    --tg-border-color: #3e3e3e;
    --tg-input-bg: #2c2c2c;
    --tg-card-bg: #2c2c2c;
    --tg-shadow: rgba(0, 0, 0, 0.3);
}

.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--tg-bg-color);
    padding: 20px;
    position: relative;
    transition: background-color 0.3s ease;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) scale(1);
        opacity: 1;
    }

    50% {
        transform: translateY(-10px) scale(1.02);
        opacity: 0.8;
    }
}

.auth-card {
    background: var(--tg-card-bg);
    border-radius: 12px;
    box-shadow: 0 2px 10px var(--tg-shadow);
    padding: 48px;
    width: 100%;
    max-width: 400px;
    animation: slideUp 0.3s ease-out;
    position: relative;
    border: 1px solid var(--tg-border-color);
    transition: all 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.auth-header::before {
    content: '';
    display: block;
    width: 80px;
    height: 80px;
    margin: 0 auto 24px;
    background: var(--tg-button-color);
    border-radius: 50%;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>');
    background-size: 40px;
    background-repeat: no-repeat;
    background-position: center;
    box-shadow: 0 4px 12px rgba(36, 129, 204, 0.3);
}

[data-theme="dark"] .auth-header::before {
    box-shadow: 0 4px 12px rgba(82, 136, 193, 0.3);
}

.auth-header h1 {
    color: var(--tg-text-color);
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
    letter-spacing: -0.3px;
}

.auth-header p {
    color: var(--tg-hint-color);
    font-size: 15px;
    margin: 0;
    font-weight: 400;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    position: relative;
}

.form-group label {
    color: var(--tg-text-color);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.form-group input {
    padding: 16px 16px;
    border: 1px solid var(--tg-border-color);
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s ease;
    background: var(--tg-input-bg);
    font-weight: 400;
    color: var(--tg-text-color);
    width: 100%;
    box-sizing: border-box;
}

.form-group input::placeholder {
    color: var(--tg-hint-color);
    font-weight: 400;
}

.form-group input:focus {
    outline: none;
    border-color: var(--tg-button-color);
    box-shadow: 0 0 0 2px rgba(36, 129, 204, 0.2);
}

.form-group input:hover:not(:focus) {
    border-color: var(--tg-link-color);
}

.form-group input:disabled {
    background: var(--tg-secondary-bg);
    cursor: not-allowed;
    opacity: 0.6;
}

.auth-button {
    background: var(--tg-button-color);
    color: var(--tg-button-text);
    border: none;
    padding: 14px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 20px;
    width: 100%;
    box-sizing: border-box;
}

.auth-button:hover:not(:disabled) {
    opacity: 0.9;
    transform: translateY(-1px);
}

.auth-button:active:not(:disabled) {
    transform: translateY(0);
}

.auth-button:disabled {
    background: var(--tg-hint-color);
    cursor: not-allowed;
    transform: none;
    opacity: 0.6;
}

.error-message {
    background: rgba(229, 62, 62, 0.1);
    color: var(--tg-destructive);
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid rgba(229, 62, 62, 0.2);
    font-size: 14px;
    text-align: center;
    font-weight: 400;
}

.auth-footer {
    text-align: center;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid var(--tg-border-color);
}

.auth-footer p {
    color: var(--tg-hint-color);
    font-size: 14px;
    margin: 0;
    font-weight: 400;
}

.auth-link {
    color: var(--tg-link-color);
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.2s ease;
}

.auth-link:hover {
    opacity: 0.8;
}

/* Theme toggle button */
.theme-toggle {
    position: absolute;
    top: 20px;
    right: 20px;
    background: var(--tg-card-bg);
    border: 1px solid var(--tg-border-color);
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 20px;
    box-shadow: 0 2px 8px var(--tg-shadow);
}

.theme-toggle:hover {
    transform: scale(1.05);
}

/* Responsive design */
@media (max-width: 768px) {
    .auth-container {
        padding: 16px;
    }

    .auth-card {
        padding: 32px 24px;
        max-width: 100%;
    }

    .auth-header::before {
        width: 64px;
        height: 64px;
        background-size: 32px;
    }

    .auth-header h1 {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .auth-container {
        padding: 12px;
    }

    .auth-card {
        padding: 24px 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .auth-header h1 {
        font-size: 22px;
    }

    .theme-toggle {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }
}