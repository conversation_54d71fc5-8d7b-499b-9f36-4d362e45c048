/* Telegram Authentic Design System */
:root {
    /* Light Theme - Telegram Official Colors */
    --tg-bg-color: #ffffff;
    --tg-secondary-bg: #f7f7f7;
    --tg-text-color: #000000;
    --tg-hint-color: #999999;
    --tg-link-color: #168acd;
    --tg-button-color: #40a7e3;
    --tg-button-text: #ffffff;
    --tg-destructive: #e53935;
    --tg-border-color: #dbdbdb;
    --tg-input-bg: #ffffff;
    --tg-card-bg: #ffffff;
    --tg-shadow: rgba(0, 0, 0, 0.1);
    --tg-accent: #168acd;
    --tg-success: #4caf50;
    --tg-warning: #ff9800;
}

[data-theme="dark"] {
    /* Dark Theme - Telegram Official Dark Colors */
    --tg-bg-color: #212121;
    --tg-secondary-bg: #303030;
    --tg-text-color: #ffffff;
    --tg-hint-color: #aaaaaa;
    --tg-link-color: #64b5f6;
    --tg-button-color: #64b5f6;
    --tg-button-text: #ffffff;
    --tg-destructive: #f44336;
    --tg-border-color: #424242;
    --tg-input-bg: #303030;
    --tg-card-bg: #2e2e2e;
    --tg-shadow: rgba(0, 0, 0, 0.3);
    --tg-accent: #64b5f6;
    --tg-success: #66bb6a;
    --tg-warning: #ffb74d;
}

/* Telegram Authentic Auth Container */
.auth-container {
    min-height: 100vh;
    background: var(--tg-bg-color);
    display: flex;
    flex-direction: column;
    position: relative;
    transition: background-color 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

/* Telegram-style header with logo */
.auth-header-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: var(--tg-card-bg);
    border-bottom: 1px solid var(--tg-border-color);
    position: sticky;
    top: 0;
    z-index: 100;
}

.auth-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 20px;
    font-weight: 500;
    color: var(--tg-text-color);
}

.auth-logo-icon {
    width: 32px;
    height: 32px;
    background: var(--tg-button-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) scale(1);
        opacity: 1;
    }

    50% {
        transform: translateY(-10px) scale(1.02);
        opacity: 0.8;
    }
}

/* Main content area */
.auth-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    max-width: 400px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
}

/* Telegram-style form card */
.auth-card {
    width: 100%;
    background: var(--tg-card-bg);
    border-radius: 0;
    box-shadow: none;
    border: none;
    padding: 0;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Telegram-style page header */
.auth-page-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 0 20px;
}

.auth-page-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto 24px;
    background: var(--tg-button-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 60px;
    color: white;
    box-shadow: 0 8px 32px rgba(64, 167, 227, 0.3);
}

[data-theme="dark"] .auth-page-icon {
    box-shadow: 0 8px 32px rgba(100, 181, 246, 0.3);
}

.auth-page-title {
    font-size: 32px;
    font-weight: 400;
    color: var(--tg-text-color);
    margin: 0 0 12px 0;
    letter-spacing: -0.5px;
}

.auth-page-subtitle {
    font-size: 16px;
    color: var(--tg-hint-color);
    margin: 0;
    line-height: 1.4;
    font-weight: 400;
}

/* Telegram-style form */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 0;
    width: 100%;
}

.form-row {
    display: flex;
    flex-direction: column;
    gap: 0;
}

.form-group {
    position: relative;
    margin-bottom: 1px;
}

.form-group label {
    display: none;
    /* Telegram doesn't show labels, uses placeholders */
}

/* Telegram-style inputs */
.form-group input {
    width: 100%;
    padding: 18px 20px;
    border: none;
    border-bottom: 1px solid var(--tg-border-color);
    background: var(--tg-input-bg);
    font-size: 17px;
    color: var(--tg-text-color);
    transition: all 0.2s ease;
    border-radius: 0;
    box-sizing: border-box;
    font-family: inherit;
}

.form-group:first-child input {
    border-top: 1px solid var(--tg-border-color);
    border-radius: 12px 12px 0 0;
}

.form-group:last-child input {
    border-radius: 0 0 12px 12px;
}

.form-group:only-child input {
    border-radius: 12px;
}

.form-group input::placeholder {
    color: var(--tg-hint-color);
    font-weight: 400;
}

.form-group input:focus {
    outline: none;
    border-bottom-color: var(--tg-button-color);
    background: var(--tg-card-bg);
}

.form-group input:hover:not(:focus) {
    background: var(--tg-secondary-bg);
}

.form-group input:disabled {
    background: var(--tg-secondary-bg);
    cursor: not-allowed;
    opacity: 0.6;
}

/* Telegram-style button */
.auth-button {
    background: var(--tg-button-color);
    color: var(--tg-button-text);
    border: none;
    padding: 16px 24px;
    border-radius: 12px;
    font-size: 17px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 32px;
    width: 100%;
    box-sizing: border-box;
    font-family: inherit;
    letter-spacing: 0.3px;
}

.auth-button:hover:not(:disabled) {
    background: #3498db;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(64, 167, 227, 0.3);
}

.auth-button:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(64, 167, 227, 0.2);
}

.auth-button:disabled {
    background: var(--tg-hint-color);
    cursor: not-allowed;
    transform: none;
    opacity: 0.6;
    box-shadow: none;
}

/* Telegram-style error message */
.error-message {
    background: rgba(229, 62, 62, 0.1);
    color: var(--tg-destructive);
    padding: 16px 20px;
    border-radius: 12px;
    border: 1px solid rgba(229, 62, 62, 0.2);
    font-size: 15px;
    text-align: center;
    font-weight: 400;
    margin-bottom: 20px;
    line-height: 1.4;
}

/* Telegram-style footer */
.auth-footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
}

.auth-footer p {
    color: var(--tg-hint-color);
    font-size: 15px;
    margin: 0;
    font-weight: 400;
    line-height: 1.4;
}

.auth-link {
    color: var(--tg-link-color);
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.2s ease;
}

.auth-link:hover {
    opacity: 0.8;
}

/* Telegram-style theme toggle */
.theme-toggle {
    background: transparent;
    border: none;
    color: var(--tg-text-color);
    cursor: pointer;
    font-size: 20px;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.theme-toggle:hover {
    background: var(--tg-secondary-bg);
}

/* Telegram-style responsive design */
@media (max-width: 768px) {
    .auth-content {
        padding: 16px;
    }

    .auth-page-icon {
        width: 100px;
        height: 100px;
        font-size: 50px;
    }

    .auth-page-title {
        font-size: 28px;
    }

    .auth-page-subtitle {
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .auth-content {
        padding: 12px;
    }

    .auth-page-header {
        margin-bottom: 32px;
    }

    .auth-page-icon {
        width: 80px;
        height: 80px;
        font-size: 40px;
        margin-bottom: 20px;
    }

    .auth-page-title {
        font-size: 24px;
    }

    .auth-page-subtitle {
        font-size: 14px;
    }

    .form-group input {
        padding: 16px 18px;
        font-size: 16px;
    }

    .auth-button {
        padding: 14px 20px;
        font-size: 16px;
    }

    .theme-toggle {
        width: 36px;
        height: 36px;
        font-size: 18px;
    }
}