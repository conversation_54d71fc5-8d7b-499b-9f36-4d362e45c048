.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    animation: float 15s ease-in-out infinite;
    pointer-events: none;
}

.auth-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="hexagons" width="60" height="60" patternUnits="userSpaceOnUse"><polygon points="30,2 52,17 52,43 30,58 8,43 8,17" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23hexagons)"/></svg>');
    pointer-events: none;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) scale(1);
        opacity: 1;
    }

    50% {
        transform: translateY(-10px) scale(1.02);
        opacity: 0.8;
    }
}

.auth-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(30px);
    border-radius: 24px;
    box-shadow:
        0 32px 64px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    padding: 56px 48px;
    width: 100%;
    max-width: 440px;
    animation: slideUp 0.8s cubic-bezier(0.16, 1, 0.3, 1);
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.6), transparent);
}

@keyframes slideUp {
    0% {
        opacity: 0;
        transform: translateY(60px) scale(0.95);
        filter: blur(10px);
    }

    50% {
        opacity: 0.8;
        transform: translateY(20px) scale(0.98);
        filter: blur(5px);
    }

    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0px);
    }
}

.auth-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

.auth-header::before {
    content: '💬';
    font-size: 48px;
    display: block;
    margin-bottom: 16px;
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.3));
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-8px);
    }
}

.auth-header h1 {
    color: #1a202c;
    font-size: 36px;
    font-weight: 800;
    margin: 0 0 16px 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.5px;
}

.auth-header p {
    color: #64748b;
    font-size: 16px;
    margin: 0;
    font-weight: 400;
    opacity: 0.8;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    position: relative;
}

.form-group label {
    color: #374151;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 12px;
}

.form-group input {
    padding: 18px 24px;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    font-size: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    font-weight: 400;
    color: #1a202c;
    position: relative;
}

.form-group input::placeholder {
    color: #94a3b8;
    font-weight: 400;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.1),
        0 8px 25px rgba(102, 126, 234, 0.15);
    background: rgba(255, 255, 255, 0.98);
    transform: translateY(-2px);
}

.form-group input:hover:not(:focus) {
    border-color: #cbd5e0;
    transform: translateY(-1px);
}

.form-group input:disabled {
    background: rgba(248, 250, 252, 0.5);
    cursor: not-allowed;
    opacity: 0.6;
}

.form-group input::placeholder {
    color: #94a3b8;
    transition: color 0.3s ease;
}

.form-group input:focus::placeholder {
    color: #cbd5e0;
}

/* Loading state for inputs */
.form-group.loading input {
    background: linear-gradient(90deg, rgba(248, 250, 252, 0.9) 25%, rgba(241, 245, 249, 0.9) 50%, rgba(248, 250, 252, 0.9) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }

    100% {
        background-position: 200% 0;
    }
}

.auth-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 20px 32px;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: 24px;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 8px 32px rgba(102, 126, 234, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 14px;
}

.auth-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.auth-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.auth-button:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow:
        0 12px 40px rgba(102, 126, 234, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
}

.auth-button:hover:not(:disabled)::before {
    left: 100%;
}

.auth-button:hover:not(:disabled)::after {
    width: 300px;
    height: 300px;
}

.auth-button:active:not(:disabled) {
    transform: translateY(-1px);
    box-shadow:
        0 6px 20px rgba(102, 126, 234, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
}

.auth-button:disabled {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
    color: #94a3b8;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.error-message {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    color: #dc2626;
    padding: 16px 20px;
    border-radius: 12px;
    border: 1px solid #fecaca;
    font-size: 14px;
    text-align: center;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.1);
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {

    0%,
    100% {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-5px);
    }

    75% {
        transform: translateX(5px);
    }
}

.auth-footer {
    text-align: center;
    margin-top: 40px;
    padding-top: 32px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    position: relative;
}

.auth-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
}

.auth-footer p {
    color: #64748b;
    font-size: 15px;
    margin: 0;
    font-weight: 400;
}

.auth-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
}

.auth-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

.auth-link:hover {
    color: #764ba2;
    transform: translateY(-1px);
}

.auth-link:hover::after {
    width: 100%;
}

/* Responsive design */
@media (max-width: 768px) {
    .auth-container {
        padding: 16px;
    }

    .auth-card {
        padding: 40px 32px;
        max-width: 100%;
    }

    .auth-header h1 {
        font-size: 32px;
    }

    .form-group input {
        padding: 16px 20px;
    }

    .auth-button {
        padding: 18px 28px;
    }
}

@media (max-width: 480px) {
    .auth-container {
        padding: 12px;
    }

    .auth-card {
        padding: 32px 24px;
        border-radius: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .auth-header h1 {
        font-size: 28px;
    }

    .auth-header::before {
        font-size: 40px;
    }

    .form-group input {
        padding: 14px 18px;
        font-size: 15px;
    }

    .auth-button {
        padding: 16px 24px;
        font-size: 13px;
    }
}