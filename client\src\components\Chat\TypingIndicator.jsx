import React from 'react';
import './TypingIndicator.css';

const TypingIndicator = ({ typingUsers }) => {
    if (!typingUsers || typingUsers.size === 0) {
        return null;
    }

    const userCount = typingUsers.size;
    
    const getTypingText = () => {
        if (userCount === 1) {
            return 'Someone is typing...';
        } else if (userCount === 2) {
            return '2 people are typing...';
        } else {
            return `${userCount} people are typing...`;
        }
    };

    return (
        <div className="typing-indicator">
            <div className="typing-avatar">
                <div className="avatar-placeholder">
                    <span className="avatar-initials">...</span>
                </div>
            </div>
            
            <div className="typing-bubble">
                <div className="typing-dots">
                    <span className="dot"></span>
                    <span className="dot"></span>
                    <span className="dot"></span>
                </div>
            </div>
            
            <div className="typing-text">
                {getTypingText()}
            </div>
        </div>
    );
};

export default TypingIndicator;
