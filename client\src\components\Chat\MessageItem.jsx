import React from 'react';
import './MessageItem.css';

const MessageItem = ({ message, isOwn, isFirstInGroup, isLastInGroup }) => {
    const formatTime = (timestamp) => {
        return new Date(timestamp).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });
    };

    const getInitials = (name) => {
        if (!name) return '?';
        return name
            .split(' ')
            .map(word => word[0])
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    const getSenderName = () => {
        if (message.first_name || message.last_name) {
            return `${message.first_name || ''} ${message.last_name || ''}`.trim();
        }
        return message.username || 'Unknown';
    };

    return (
        <div className={`message-item ${isOwn ? 'own' : 'other'} ${isFirstInGroup ? 'first-in-group' : ''} ${isLastInGroup ? 'last-in-group' : ''}`}>
            {!isOwn && isFirstInGroup && (
                <div className="message-avatar">
                    {message.avatar_url ? (
                        <img src={message.avatar_url} alt="Avatar" />
                    ) : (
                        <div className="avatar-placeholder">
                            <span className="avatar-initials">
                                {getInitials(getSenderName())}
                            </span>
                        </div>
                    )}
                </div>
            )}
            
            <div className="message-content">
                {!isOwn && isFirstInGroup && (
                    <div className="message-sender">
                        {getSenderName()}
                    </div>
                )}
                
                <div className="message-bubble">
                    <div className="message-text">
                        {message.content}
                    </div>
                    
                    <div className="message-meta">
                        <span className="message-time">
                            {formatTime(message.timestamp)}
                        </span>
                        {isOwn && (
                            <span className="message-status">
                                {message.status === 'sent' && '✓'}
                                {message.status === 'delivered' && '✓✓'}
                                {message.status === 'read' && '✓✓'}
                            </span>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default MessageItem;
