.chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: white;
    border-bottom: 1px solid #e1e5e9;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.chat-header-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
}

.chat-avatar {
    position: relative;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
}

.chat-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, #0088cc, #0066aa);
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-initials {
    color: white;
    font-weight: 600;
    font-size: 16px;
}

.conversation-type-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 18px;
    height: 18px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 9px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.chat-details {
    flex: 1;
    min-width: 0;
}

.chat-name {
    font-weight: 600;
    color: #333;
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 2px;
}

.chat-status {
    font-size: 13px;
    color: #28a745;
}

.chat-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: transparent;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: background-color 0.2s ease;
    color: #666;
}

.action-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.action-btn:active {
    background: rgba(0, 0, 0, 0.15);
}

/* Responsive design */
@media (max-width: 768px) {
    .chat-header {
        padding: 12px 16px;
    }
    
    .chat-name {
        font-size: 15px;
    }
    
    .chat-status {
        font-size: 12px;
    }
    
    .action-btn {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }
}
