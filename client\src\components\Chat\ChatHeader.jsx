import React from 'react';
import './ChatHeader.css';

const ChatHeader = ({ conversation, conversationName }) => {
    const getInitials = (name) => {
        return name
            .split(' ')
            .map(word => word[0])
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    const getMemberCount = () => {
        if (conversation.type === 'group') {
            return conversation.member_count || 0;
        }
        return null;
    };

    const getStatusText = () => {
        if (conversation.type === 'direct') {
            return 'Online'; // You can implement real online status later
        } else if (conversation.type === 'group') {
            const memberCount = getMemberCount();
            return memberCount ? `${memberCount} members` : 'Group';
        }
        return '';
    };

    return (
        <div className="chat-header">
            <div className="chat-header-info">
                <div className="chat-avatar">
                    {conversation.avatar_url ? (
                        <img src={conversation.avatar_url} alt="Avatar" />
                    ) : (
                        <div className="avatar-placeholder">
                            <span className="avatar-initials">
                                {getInitials(conversationName)}
                            </span>
                        </div>
                    )}
                    {conversation.type === 'group' && (
                        <div className="conversation-type-indicator">👥</div>
                    )}
                </div>
                
                <div className="chat-details">
                    <div className="chat-name">{conversationName}</div>
                    <div className="chat-status">{getStatusText()}</div>
                </div>
            </div>

            <div className="chat-actions">
                <button 
                    className="action-btn"
                    title="Search in conversation"
                    onClick={() => {/* TODO: Implement search */}}
                >
                    🔍
                </button>
                <button 
                    className="action-btn"
                    title="Call"
                    onClick={() => {/* TODO: Implement call */}}
                >
                    📞
                </button>
                <button 
                    className="action-btn"
                    title="More options"
                    onClick={() => {/* TODO: Implement options menu */}}
                >
                    ⋮
                </button>
            </div>
        </div>
    );
};

export default ChatHeader;
