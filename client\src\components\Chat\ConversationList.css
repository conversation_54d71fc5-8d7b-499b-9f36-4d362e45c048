.conversation-list {
    display: flex;
    flex-direction: column;
}

.conversation-list-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: var(--tg-hint-color);
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.conversation-list-empty p {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px 0;
    color: var(--tg-text-color);
}

.conversation-list-empty span {
    font-size: 14px;
    color: var(--tg-hint-color);
}

/* Conversation Item Styles */
.conversation-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--tg-border-color);
    position: relative;
}

.conversation-item:hover {
    background: var(--tg-secondary-bg);
}

.conversation-item.active {
    background: var(--tg-secondary-bg);
}

.conversation-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--tg-button-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 500;
    font-size: 18px;
    margin-right: 12px;
    flex-shrink: 0;
}

.conversation-info {
    flex: 1;
    min-width: 0;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.conversation-name {
    font-weight: 500;
    color: var(--tg-text-color);
    font-size: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.conversation-time {
    font-size: 13px;
    color: var(--tg-hint-color);
    flex-shrink: 0;
    margin-left: 8px;
}

.conversation-preview {
    font-size: 14px;
    color: var(--tg-hint-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
}

.conversation-unread {
    background: var(--tg-button-color);
    color: white;
    border-radius: 50%;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    margin-left: 8px;
}