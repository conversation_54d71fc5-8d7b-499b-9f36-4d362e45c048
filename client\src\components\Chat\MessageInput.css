.message-input-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    padding: 20px 24px;
    box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.05);
}

.message-input {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 28px;
    padding: 12px 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.08),
        0 0 0 1px rgba(255, 255, 255, 0.2);
}

.message-input:focus-within {
    background: rgba(232, 240, 254, 0.9);
    box-shadow:
        0 4px 20px rgba(0, 136, 204, 0.15),
        0 0 0 2px rgba(0, 136, 204, 0.2);
    transform: translateY(-2px);
}

.input-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: background-color 0.2s ease;
    flex-shrink: 0;
}

.input-action-btn:hover:not(:disabled) {
    background: rgba(0, 0, 0, 0.1);
}

.input-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.input-field {
    flex: 1;
    min-height: 32px;
    display: flex;
    align-items: center;
}

.message-textarea {
    width: 100%;
    border: none;
    background: transparent;
    outline: none;
    resize: none;
    font-size: 14px;
    line-height: 1.4;
    padding: 6px 0;
    font-family: inherit;
    color: #333;
    min-height: 20px;
    max-height: 120px;
    overflow-y: auto;
}

.message-textarea::placeholder {
    color: #999;
}

.message-textarea:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.send-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: linear-gradient(135deg, #0088cc, #005fa3);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    opacity: 0.6;
    transform: scale(0.9);
    box-shadow: 0 2px 8px rgba(0, 136, 204, 0.3);
}

.send-btn.active {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(0, 136, 204, 0.4);
}

.send-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #0099dd, #0066aa);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 136, 204, 0.5);
}

.send-btn:active:not(:disabled) {
    transform: scale(0.95);
}

.send-btn:disabled {
    background: linear-gradient(135deg, #ccc, #999);
    cursor: not-allowed;
    transform: scale(0.9);
    box-shadow: none;
}

/* Scrollbar for textarea */
.message-textarea::-webkit-scrollbar {
    width: 4px;
}

.message-textarea::-webkit-scrollbar-track {
    background: transparent;
}

.message-textarea::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

/* Responsive design */
@media (max-width: 768px) {
    .message-input-container {
        padding: 12px 16px;
    }

    .message-input {
        padding: 6px 10px;
    }

    .input-action-btn {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }

    .send-btn {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }

    .message-textarea {
        font-size: 13px;
    }
}