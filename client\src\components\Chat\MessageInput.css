.message-input-container {
    background: white;
    border-top: 1px solid #e1e5e9;
    padding: 16px 20px;
}

.message-input {
    display: flex;
    align-items: flex-end;
    gap: 8px;
    background: #f0f2f5;
    border-radius: 24px;
    padding: 8px 12px;
    transition: all 0.2s ease;
}

.message-input:focus-within {
    background: #e8f0fe;
    box-shadow: 0 0 0 2px rgba(0, 136, 204, 0.2);
}

.input-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: background-color 0.2s ease;
    flex-shrink: 0;
}

.input-action-btn:hover:not(:disabled) {
    background: rgba(0, 0, 0, 0.1);
}

.input-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.input-field {
    flex: 1;
    min-height: 32px;
    display: flex;
    align-items: center;
}

.message-textarea {
    width: 100%;
    border: none;
    background: transparent;
    outline: none;
    resize: none;
    font-size: 14px;
    line-height: 1.4;
    padding: 6px 0;
    font-family: inherit;
    color: #333;
    min-height: 20px;
    max-height: 120px;
    overflow-y: auto;
}

.message-textarea::placeholder {
    color: #999;
}

.message-textarea:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.send-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #0088cc;
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.2s ease;
    flex-shrink: 0;
    opacity: 0.5;
    transform: scale(0.9);
}

.send-btn.active {
    opacity: 1;
    transform: scale(1);
}

.send-btn:hover:not(:disabled) {
    background: #0066aa;
    transform: scale(1.05);
}

.send-btn:active:not(:disabled) {
    transform: scale(0.95);
}

.send-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: scale(0.9);
}

/* Scrollbar for textarea */
.message-textarea::-webkit-scrollbar {
    width: 4px;
}

.message-textarea::-webkit-scrollbar-track {
    background: transparent;
}

.message-textarea::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

/* Responsive design */
@media (max-width: 768px) {
    .message-input-container {
        padding: 12px 16px;
    }
    
    .message-input {
        padding: 6px 10px;
    }
    
    .input-action-btn {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }
    
    .send-btn {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }
    
    .message-textarea {
        font-size: 13px;
    }
}
