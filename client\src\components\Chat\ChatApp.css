.chat-app {
    height: 100vh;
    background: var(--tg-bg-color);
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    display: flex;
    flex-direction: column;
}

.chat-layout {
    display: flex;
    height: 100vh;
    transition: all 0.3s ease;
}

.chat-sidebar {
    width: 420px;
    background: var(--tg-card-bg);
    border-right: 1px solid var(--tg-border-color);
    flex-shrink: 0;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.sidebar-collapsed .chat-sidebar {
    width: 80px;
}

.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--tg-secondary-bg);
    position: relative;
}

.chat-welcome {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--tg-card-bg);
    position: relative;
}

.welcome-content {
    text-align: center;
    max-width: 360px;
    padding: 32px;
}

.welcome-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.6;
}

.welcome-content h2 {
    color: var(--tg-text-color);
    font-size: 24px;
    font-weight: 500;
    margin: 0 0 12px 0;
}

.welcome-content p {
    color: var(--tg-hint-color);
    font-size: 15px;
    margin: 0 0 32px 0;
    font-weight: 400;
    line-height: 1.4;
}

.welcome-features {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
}

.feature {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--tg-hint-color);
    font-size: 13px;
}

.feature-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
    .chat-layout {
        position: relative;
    }

    .chat-sidebar {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .chat-sidebar.mobile-open {
        transform: translateX(0);
    }

    .sidebar-collapsed .chat-sidebar {
        width: 350px;
    }

    .chat-main {
        width: 100%;
        margin: 0;
    }

    .chat-welcome {
        margin: 10px;
    }

    .welcome-content {
        padding: 20px;
    }

    .welcome-icon {
        font-size: 48px;
    }

    .welcome-content h2 {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .welcome-features {
        gap: 12px;
    }

    .feature {
        font-size: 13px;
    }

    .feature-icon {
        font-size: 16px;
        width: 20px;
    }
}