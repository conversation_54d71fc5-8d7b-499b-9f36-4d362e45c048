.chat-app {
    height: 100vh;
    background: #f0f2f5;
    overflow: hidden;
}

.chat-layout {
    display: flex;
    height: 100%;
    transition: all 0.3s ease;
}

.chat-sidebar {
    width: 350px;
    background: white;
    border-right: 1px solid #e1e5e9;
    flex-shrink: 0;
    transition: width 0.3s ease;
}

.sidebar-collapsed .chat-sidebar {
    width: 80px;
}

.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f0f2f5;
}

.chat-welcome {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    margin: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.welcome-content {
    text-align: center;
    max-width: 400px;
    padding: 40px;
}

.welcome-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.8;
}

.welcome-content h2 {
    color: #333;
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 12px 0;
}

.welcome-content p {
    color: #666;
    font-size: 16px;
    margin: 0 0 30px 0;
}

.welcome-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
}

.feature {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #555;
    font-size: 14px;
}

.feature-icon {
    font-size: 18px;
    width: 24px;
    text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
    .chat-layout {
        position: relative;
    }
    
    .chat-sidebar {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .chat-sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .sidebar-collapsed .chat-sidebar {
        width: 350px;
    }
    
    .chat-main {
        width: 100%;
        margin: 0;
    }
    
    .chat-welcome {
        margin: 10px;
    }
    
    .welcome-content {
        padding: 20px;
    }
    
    .welcome-icon {
        font-size: 48px;
    }
    
    .welcome-content h2 {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .welcome-features {
        gap: 12px;
    }
    
    .feature {
        font-size: 13px;
    }
    
    .feature-icon {
        font-size: 16px;
        width: 20px;
    }
}
