.chat-app {
    height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.chat-layout {
    display: flex;
    height: 100%;
    transition: all 0.3s ease;
}

.chat-sidebar {
    width: 380px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(0, 0, 0, 0.08);
    flex-shrink: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.05);
}

.sidebar-collapsed .chat-sidebar {
    width: 80px;
}

.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f0f2f5;
}

.chat-welcome {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    margin: 24px;
    border-radius: 20px;
    box-shadow:
        0 10px 40px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.chat-welcome::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 136, 204, 0.3), transparent);
}

.welcome-content {
    text-align: center;
    max-width: 400px;
    padding: 40px;
}

.welcome-icon {
    font-size: 80px;
    margin-bottom: 24px;
    opacity: 0.9;
    filter: drop-shadow(0 4px 8px rgba(0, 136, 204, 0.2));
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.welcome-content h2 {
    color: #1a202c;
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 16px 0;
    background: linear-gradient(135deg, #0088cc, #005fa3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-content p {
    color: #64748b;
    font-size: 18px;
    margin: 0 0 40px 0;
    font-weight: 400;
}

.welcome-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
}

.feature {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #555;
    font-size: 14px;
}

.feature-icon {
    font-size: 18px;
    width: 24px;
    text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
    .chat-layout {
        position: relative;
    }

    .chat-sidebar {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .chat-sidebar.mobile-open {
        transform: translateX(0);
    }

    .sidebar-collapsed .chat-sidebar {
        width: 350px;
    }

    .chat-main {
        width: 100%;
        margin: 0;
    }

    .chat-welcome {
        margin: 10px;
    }

    .welcome-content {
        padding: 20px;
    }

    .welcome-icon {
        font-size: 48px;
    }

    .welcome-content h2 {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .welcome-features {
        gap: 12px;
    }

    .feature {
        font-size: 13px;
    }

    .feature-icon {
        font-size: 16px;
        width: 20px;
    }
}