import React, { createContext, useContext, useState, useEffect } from 'react';
import { conversationsAPI, messagesAPI } from '../services/api';
import socketService from '../services/socket';
import { useAuth } from './AuthContext';

const ChatContext = createContext();

export const useChat = () => {
    const context = useContext(ChatContext);
    if (!context) {
        throw new Error('useChat must be used within a ChatProvider');
    }
    return context;
};

export const ChatProvider = ({ children }) => {
    const { isAuthenticated, user } = useAuth();
    const [conversations, setConversations] = useState([]);
    const [activeConversation, setActiveConversation] = useState(null);
    const [messages, setMessages] = useState({});
    const [typingUsers, setTypingUsers] = useState({});
    const [onlineUsers, setOnlineUsers] = useState(new Set());
    const [loading, setLoading] = useState(false);

    // Load conversations when authenticated
    useEffect(() => {
        if (isAuthenticated) {
            loadConversations();
            setupSocketListeners();
        }

        return () => {
            // Cleanup socket listeners
            socketService.off('new_message', handleNewMessage);
            socketService.off('user_typing', handleUserTyping);
            socketService.off('user_status_changed', handleUserStatusChanged);
        };
    }, [isAuthenticated]);

    const setupSocketListeners = () => {
        socketService.on('new_message', handleNewMessage);
        socketService.on('user_typing', handleUserTyping);
        socketService.on('user_status_changed', handleUserStatusChanged);
        socketService.on('message_read', handleMessageRead);
    };

    const handleNewMessage = (message) => {
        const conversationId = message.conversation_id;
        
        // Add message to messages state
        setMessages(prev => ({
            ...prev,
            [conversationId]: [...(prev[conversationId] || []), message]
        }));

        // Update conversation's last message
        setConversations(prev => 
            prev.map(conv => 
                conv.id === conversationId 
                    ? { ...conv, last_message_at: message.timestamp }
                    : conv
            ).sort((a, b) => new Date(b.last_message_at) - new Date(a.last_message_at))
        );
    };

    const handleUserTyping = ({ user_id, conversation_id, typing }) => {
        setTypingUsers(prev => {
            const conversationTyping = prev[conversation_id] || new Set();
            const newTyping = new Set(conversationTyping);
            
            if (typing) {
                newTyping.add(user_id);
            } else {
                newTyping.delete(user_id);
            }
            
            return {
                ...prev,
                [conversation_id]: newTyping
            };
        });

        // Clear typing indicator after 3 seconds
        if (typing) {
            setTimeout(() => {
                setTypingUsers(prev => {
                    const conversationTyping = prev[conversation_id] || new Set();
                    const newTyping = new Set(conversationTyping);
                    newTyping.delete(user_id);
                    
                    return {
                        ...prev,
                        [conversation_id]: newTyping
                    };
                });
            }, 3000);
        }
    };

    const handleUserStatusChanged = ({ user_id, status, is_online }) => {
        setOnlineUsers(prev => {
            const newOnlineUsers = new Set(prev);
            if (is_online) {
                newOnlineUsers.add(user_id);
            } else {
                newOnlineUsers.delete(user_id);
            }
            return newOnlineUsers;
        });
    };

    const handleMessageRead = ({ user_id, message_id, conversation_id }) => {
        // Handle read receipts - could update message status
        console.log(`User ${user_id} read message ${message_id} in conversation ${conversation_id}`);
    };

    const loadConversations = async () => {
        try {
            setLoading(true);
            const response = await conversationsAPI.getAll();
            setConversations(response.data);
        } catch (error) {
            console.error('Error loading conversations:', error);
        } finally {
            setLoading(false);
        }
    };

    const loadMessages = async (conversationId, limit = 50, offset = 0) => {
        try {
            const response = await messagesAPI.getConversationMessages(conversationId, limit, offset);
            const conversationMessages = response.data.messages;
            
            setMessages(prev => ({
                ...prev,
                [conversationId]: offset === 0 ? conversationMessages : [
                    ...conversationMessages,
                    ...(prev[conversationId] || [])
                ]
            }));

            return conversationMessages;
        } catch (error) {
            console.error('Error loading messages:', error);
            return [];
        }
    };

    const sendMessage = async (conversationId, content, messageType = 'text', replyToMessageId = null) => {
        try {
            // Send via socket for real-time delivery
            socketService.sendMessage(conversationId, content, messageType, replyToMessageId);
            
            // Also send via API as backup
            await messagesAPI.send({
                conversation_id: conversationId,
                content,
                message_type: messageType,
                reply_to_message_id: replyToMessageId
            });

            return { success: true };
        } catch (error) {
            console.error('Error sending message:', error);
            return { success: false, error: error.message };
        }
    };

    const createConversation = async (conversationData) => {
        try {
            const response = await conversationsAPI.create(conversationData);
            const newConversation = response.data.conversation;
            
            setConversations(prev => [newConversation, ...prev]);
            
            // Join the conversation room
            socketService.joinConversation(newConversation.id);
            
            return { success: true, conversation: newConversation };
        } catch (error) {
            console.error('Error creating conversation:', error);
            return { success: false, error: error.message };
        }
    };

    const createDirectConversation = async (userId) => {
        try {
            const response = await conversationsAPI.createDirect(userId);
            const conversation = response.data.conversation;
            
            // Check if conversation already exists
            const existingConv = conversations.find(c => c.id === conversation.id);
            if (!existingConv) {
                setConversations(prev => [conversation, ...prev]);
            }
            
            // Join the conversation room
            socketService.joinConversation(conversation.id);
            
            return { success: true, conversation };
        } catch (error) {
            console.error('Error creating direct conversation:', error);
            return { success: false, error: error.message };
        }
    };

    const selectConversation = async (conversation) => {
        setActiveConversation(conversation);
        
        // Join conversation room
        socketService.joinConversation(conversation.id);
        
        // Load messages if not already loaded
        if (!messages[conversation.id]) {
            await loadMessages(conversation.id);
        }
    };

    const startTyping = (conversationId) => {
        socketService.startTyping(conversationId);
    };

    const stopTyping = (conversationId) => {
        socketService.stopTyping(conversationId);
    };

    const value = {
        conversations,
        activeConversation,
        messages,
        typingUsers,
        onlineUsers,
        loading,
        loadConversations,
        loadMessages,
        sendMessage,
        createConversation,
        createDirectConversation,
        selectConversation,
        startTyping,
        stopTyping,
    };

    return (
        <ChatContext.Provider value={value}>
            {children}
        </ChatContext.Provider>
    );
};
