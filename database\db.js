const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');

class Database {
    constructor() {
        this.db = null;
        this.init();
    }

    init() {
        // Create database directory if it doesn't exist
        const dbDir = path.dirname(__filename);
        if (!fs.existsSync(dbDir)) {
            fs.mkdirSync(dbDir, { recursive: true });
        }

        // Connect to SQLite database
        this.db = new sqlite3.Database(path.join(__dirname, 'telegram_clone.db'), (err) => {
            if (err) {
                console.error('Error opening database:', err.message);
            } else {
                console.log('Connected to SQLite database');
                this.createTables();
            }
        });

        // Enable foreign keys
        this.db.run('PRAGMA foreign_keys = ON');
    }

    createTables() {
        const schemaPath = path.join(__dirname, 'schema.sql');
        const schema = fs.readFileSync(schemaPath, 'utf8');

        // Split schema by semicolons and execute each statement
        const statements = schema.split(';').filter(stmt => stmt.trim());

        // Execute statements sequentially to handle dependencies
        this.executeStatementsSequentially(statements, 0);
    }

    executeStatementsSequentially(statements, index) {
        if (index >= statements.length) {
            console.log('Database tables created/verified');
            return;
        }

        const statement = statements[index].trim();
        if (!statement) {
            this.executeStatementsSequentially(statements, index + 1);
            return;
        }

        this.db.run(statement, (err) => {
            if (err && !err.message.includes('already exists')) {
                console.error(`Error executing statement ${index + 1}:`, err.message);
                console.error('Statement:', statement.substring(0, 100) + '...');
            }
            // Continue with next statement regardless of error
            this.executeStatementsSequentially(statements, index + 1);
        });
    }

    // User operations
    createUser(userData) {
        return new Promise((resolve, reject) => {
            const { username, email, phone, password_hash, first_name, last_name } = userData;
            const sql = `
                INSERT INTO users (username, email, phone, password_hash, first_name, last_name)
                VALUES (?, ?, ?, ?, ?, ?)
            `;
            
            this.db.run(sql, [username, email, phone, password_hash, first_name, last_name], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, ...userData });
                }
            });
        });
    }

    getUserByUsername(username) {
        return new Promise((resolve, reject) => {
            const sql = 'SELECT * FROM users WHERE username = ?';
            this.db.get(sql, [username], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    getUserById(id) {
        return new Promise((resolve, reject) => {
            const sql = 'SELECT * FROM users WHERE id = ?';
            this.db.get(sql, [id], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    updateUserStatus(userId, status, isOnline = false) {
        return new Promise((resolve, reject) => {
            const sql = `
                UPDATE users 
                SET status = ?, is_online = ?, last_seen = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `;
            
            this.db.run(sql, [status, isOnline ? 1 : 0, userId], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ changes: this.changes });
                }
            });
        });
    }

    // Conversation operations
    createConversation(conversationData) {
        return new Promise((resolve, reject) => {
            const { type, name, description, created_by } = conversationData;
            const sql = `
                INSERT INTO conversations (type, name, description, created_by)
                VALUES (?, ?, ?, ?)
            `;
            
            this.db.run(sql, [type, name, description, created_by], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, ...conversationData });
                }
            });
        });
    }

    getUserConversations(userId) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT c.*, cm.last_read_message_id, cm.is_pinned, cm.is_muted,
                       u.username as created_by_username, u.first_name, u.last_name
                FROM conversations c
                JOIN conversation_members cm ON c.id = cm.conversation_id
                LEFT JOIN users u ON c.created_by = u.id
                WHERE cm.user_id = ? AND cm.left_at IS NULL
                ORDER BY c.last_message_at DESC, c.created_at DESC
            `;
            
            this.db.all(sql, [userId], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    addConversationMember(conversationId, userId, role = 'member') {
        return new Promise((resolve, reject) => {
            const sql = `
                INSERT INTO conversation_members (conversation_id, user_id, role)
                VALUES (?, ?, ?)
            `;
            
            this.db.run(sql, [conversationId, userId, role], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID });
                }
            });
        });
    }

    // Message operations
    createMessage(messageData) {
        return new Promise((resolve, reject) => {
            const { conversation_id, sender_id, content, message_type, reply_to_message_id } = messageData;
            const sql = `
                INSERT INTO messages (conversation_id, sender_id, content, message_type, reply_to_message_id)
                VALUES (?, ?, ?, ?, ?)
            `;
            
            this.db.run(sql, [conversation_id, sender_id, content, message_type || 'text', reply_to_message_id], function(err) {
                if (err) {
                    reject(err);
                } else {
                    // Update conversation's last message
                    const updateConvSql = `
                        UPDATE conversations 
                        SET last_message_id = ?, last_message_at = CURRENT_TIMESTAMP 
                        WHERE id = ?
                    `;
                    
                    this.db.run(updateConvSql, [this.lastID, conversation_id]);
                    resolve({ id: this.lastID, ...messageData });
                }
            }.bind(this));
        });
    }

    getConversationMessages(conversationId, limit = 50, offset = 0) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT m.*, u.username, u.first_name, u.last_name, u.avatar_url
                FROM messages m
                JOIN users u ON m.sender_id = u.id
                WHERE m.conversation_id = ? AND m.deleted_at IS NULL
                ORDER BY m.timestamp DESC
                LIMIT ? OFFSET ?
            `;
            
            this.db.all(sql, [conversationId, limit, offset], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows.reverse()); // Reverse to get chronological order
                }
            });
        });
    }

    // Session operations
    createSession(sessionData) {
        return new Promise((resolve, reject) => {
            const { user_id, session_token, device_info, ip_address } = sessionData;
            const sql = `
                INSERT INTO user_sessions (user_id, session_token, device_info, ip_address, expires_at)
                VALUES (?, ?, ?, ?, datetime('now', '+7 days'))
            `;
            
            this.db.run(sql, [user_id, session_token, device_info, ip_address], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID });
                }
            });
        });
    }

    getSessionByToken(token) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT s.*, u.* FROM user_sessions s
                JOIN users u ON s.user_id = u.id
                WHERE s.session_token = ? AND s.expires_at > CURRENT_TIMESTAMP
            `;
            
            this.db.get(sql, [token], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    deleteSession(token) {
        return new Promise((resolve, reject) => {
            const sql = 'DELETE FROM user_sessions WHERE session_token = ?';
            this.db.run(sql, [token], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ changes: this.changes });
                }
            });
        });
    }

    close() {
        if (this.db) {
            this.db.close((err) => {
                if (err) {
                    console.error('Error closing database:', err.message);
                } else {
                    console.log('Database connection closed');
                }
            });
        }
    }
}

module.exports = new Database();
