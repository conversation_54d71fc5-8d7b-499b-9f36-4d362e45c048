import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useChat } from '../../contexts/ChatContext';
import Sidebar from './Sidebar';
import ChatWindow from './ChatWindow';
import UserProfile from './UserProfile';
import './ChatApp.css';

const ChatApp = () => {
    const { user, logout } = useAuth();
    const { activeConversation } = useChat();
    const [showProfile, setShowProfile] = useState(false);
    const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

    const handleLogout = async () => {
        if (window.confirm('Are you sure you want to logout?')) {
            await logout();
        }
    };

    return (
        <div className="chat-app">
            <div className={`chat-layout ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
                {/* Sidebar */}
                <div className="chat-sidebar">
                    <Sidebar 
                        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
                        collapsed={sidebarCollapsed}
                        onShowProfile={() => setShowProfile(true)}
                        onLogout={handleLogout}
                    />
                </div>

                {/* Main Chat Area */}
                <div className="chat-main">
                    {activeConversation ? (
                        <ChatWindow conversation={activeConversation} />
                    ) : (
                        <div className="chat-welcome">
                            <div className="welcome-content">
                                <div className="welcome-icon">💬</div>
                                <h2>Welcome to Telegram Clone</h2>
                                <p>Select a conversation to start chatting</p>
                                <div className="welcome-features">
                                    <div className="feature">
                                        <span className="feature-icon">🚀</span>
                                        <span>Real-time messaging</span>
                                    </div>
                                    <div className="feature">
                                        <span className="feature-icon">👥</span>
                                        <span>Group conversations</span>
                                    </div>
                                    <div className="feature">
                                        <span className="feature-icon">🔒</span>
                                        <span>Secure and private</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* User Profile Modal */}
            {showProfile && (
                <UserProfile 
                    user={user}
                    onClose={() => setShowProfile(false)}
                />
            )}
        </div>
    );
};

export default ChatApp;
