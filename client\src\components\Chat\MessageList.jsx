import React from 'react';
import MessageItem from './MessageItem';
import TypingIndicator from './TypingIndicator';
import './MessageList.css';

const MessageList = ({ messages, currentUserId, typingUsers }) => {
    const groupMessagesByDate = (messages) => {
        const groups = {};
        
        messages.forEach(message => {
            const date = new Date(message.timestamp).toDateString();
            if (!groups[date]) {
                groups[date] = [];
            }
            groups[date].push(message);
        });
        
        return groups;
    };

    const formatDateHeader = (dateString) => {
        const date = new Date(dateString);
        const today = new Date().toDateString();
        const yesterday = new Date(Date.now() - 86400000).toDateString();
        
        if (dateString === today) {
            return 'Today';
        } else if (dateString === yesterday) {
            return 'Yesterday';
        } else {
            return date.toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });
        }
    };

    const groupedMessages = groupMessagesByDate(messages);
    const hasTypingUsers = typingUsers && typingUsers.size > 0;

    if (messages.length === 0 && !hasTypingUsers) {
        return (
            <div className="message-list-empty">
                <div className="empty-icon">💬</div>
                <p>No messages yet</p>
                <span>Send a message to start the conversation</span>
            </div>
        );
    }

    return (
        <div className="message-list">
            {Object.entries(groupedMessages).map(([date, dateMessages]) => (
                <div key={date} className="message-group">
                    <div className="date-header">
                        <span className="date-text">{formatDateHeader(date)}</span>
                    </div>
                    
                    {dateMessages.map((message, index) => {
                        const prevMessage = index > 0 ? dateMessages[index - 1] : null;
                        const nextMessage = index < dateMessages.length - 1 ? dateMessages[index + 1] : null;
                        
                        const isFirstInGroup = !prevMessage || prevMessage.sender_id !== message.sender_id;
                        const isLastInGroup = !nextMessage || nextMessage.sender_id !== message.sender_id;
                        
                        return (
                            <MessageItem
                                key={message.id}
                                message={message}
                                isOwn={message.sender_id === currentUserId}
                                isFirstInGroup={isFirstInGroup}
                                isLastInGroup={isLastInGroup}
                            />
                        );
                    })}
                </div>
            ))}
            
            {hasTypingUsers && (
                <TypingIndicator typingUsers={typingUsers} />
            )}
        </div>
    );
};

export default MessageList;
