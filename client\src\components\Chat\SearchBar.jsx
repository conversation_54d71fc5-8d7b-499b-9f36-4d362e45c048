import React from 'react';
import './SearchBar.css';

const SearchBar = ({ value, onChange, placeholder = "Search..." }) => {
    return (
        <div className="search-bar">
            <div className="search-icon">🔍</div>
            <input
                type="text"
                value={value}
                onChange={(e) => onChange(e.target.value)}
                placeholder={placeholder}
                className="search-input"
            />
            {value && (
                <button 
                    className="clear-search"
                    onClick={() => onChange('')}
                    title="Clear search"
                >
                    ✕
                </button>
            )}
        </div>
    );
};

export default SearchBar;
