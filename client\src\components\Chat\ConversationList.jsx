import React from 'react';
import { useChat } from '../../contexts/ChatContext';
import ConversationItem from './ConversationItem';
import './ConversationList.css';

const ConversationList = ({ conversations, collapsed }) => {
    const { selectConversation, activeConversation } = useChat();

    const handleSelectConversation = (conversation) => {
        selectConversation(conversation);
    };

    if (conversations.length === 0) {
        return (
            <div className="conversation-list-empty">
                {!collapsed && (
                    <>
                        <div className="empty-icon">💬</div>
                        <p>No conversations yet</p>
                        <span>Start a new chat to begin messaging</span>
                    </>
                )}
            </div>
        );
    }

    return (
        <div className="conversation-list">
            {conversations.map((conversation) => (
                <ConversationItem
                    key={conversation.id}
                    conversation={conversation}
                    isActive={activeConversation?.id === conversation.id}
                    collapsed={collapsed}
                    onClick={() => handleSelectConversation(conversation)}
                />
            ))}
        </div>
    );
};

export default ConversationList;
