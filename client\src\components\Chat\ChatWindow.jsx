import React, { useState, useEffect, useRef } from 'react';
import { useChat } from '../../contexts/ChatContext';
import { useAuth } from '../../contexts/AuthContext';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import ChatHeader from './ChatHeader';
import './ChatWindow.css';

const ChatWindow = ({ conversation }) => {
    const { user } = useAuth();
    const { 
        messages, 
        loadMessages, 
        sendMessage, 
        startTyping, 
        stopTyping,
        typingUsers 
    } = useChat();
    const [loading, setLoading] = useState(false);
    const messagesEndRef = useRef(null);
    const typingTimeoutRef = useRef(null);

    const conversationMessages = messages[conversation.id] || [];
    const conversationTypingUsers = typingUsers[conversation.id] || new Set();

    useEffect(() => {
        // Load messages when conversation changes
        if (conversation.id && !messages[conversation.id]) {
            loadConversationMessages();
        }
    }, [conversation.id]);

    useEffect(() => {
        // Scroll to bottom when new messages arrive
        scrollToBottom();
    }, [conversationMessages]);

    const loadConversationMessages = async () => {
        setLoading(true);
        try {
            await loadMessages(conversation.id);
        } catch (error) {
            console.error('Error loading messages:', error);
        } finally {
            setLoading(false);
        }
    };

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    const handleSendMessage = async (content, messageType = 'text', replyToMessageId = null) => {
        if (!content.trim()) return;

        try {
            const result = await sendMessage(conversation.id, content, messageType, replyToMessageId);
            if (result.success) {
                // Message sent successfully
                scrollToBottom();
            } else {
                console.error('Failed to send message:', result.error);
            }
        } catch (error) {
            console.error('Error sending message:', error);
        }
    };

    const handleTypingStart = () => {
        startTyping(conversation.id);
        
        // Clear existing timeout
        if (typingTimeoutRef.current) {
            clearTimeout(typingTimeoutRef.current);
        }
        
        // Set timeout to stop typing after 3 seconds
        typingTimeoutRef.current = setTimeout(() => {
            stopTyping(conversation.id);
        }, 3000);
    };

    const handleTypingStop = () => {
        stopTyping(conversation.id);
        
        if (typingTimeoutRef.current) {
            clearTimeout(typingTimeoutRef.current);
            typingTimeoutRef.current = null;
        }
    };

    const getConversationName = () => {
        if (conversation.type === 'direct') {
            if (conversation.created_by_username && conversation.created_by_username !== user.username) {
                return conversation.first_name && conversation.last_name 
                    ? `${conversation.first_name} ${conversation.last_name}`
                    : conversation.created_by_username;
            }
            return 'Direct Chat';
        }
        return conversation.name || 'Group Chat';
    };

    return (
        <div className="chat-window">
            <ChatHeader 
                conversation={conversation}
                conversationName={getConversationName()}
            />
            
            <div className="chat-messages">
                {loading ? (
                    <div className="messages-loading">
                        <div className="loading-spinner medium"></div>
                        <span>Loading messages...</span>
                    </div>
                ) : (
                    <MessageList 
                        messages={conversationMessages}
                        currentUserId={user.id}
                        typingUsers={conversationTypingUsers}
                    />
                )}
                <div ref={messagesEndRef} />
            </div>

            <MessageInput 
                onSendMessage={handleSendMessage}
                onTypingStart={handleTypingStart}
                onTypingStop={handleTypingStop}
                disabled={loading}
            />
        </div>
    );
};

export default ChatWindow;
