import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useChat } from '../../contexts/ChatContext';
import ConversationList from './ConversationList';
import SearchBar from './SearchBar';
import './Sidebar.css';

const Sidebar = ({ onToggleCollapse, collapsed, onShowProfile, onLogout }) => {
    const { user } = useAuth();
    const { conversations, loading } = useChat();
    const [searchQuery, setSearchQuery] = useState('');
    const [filteredConversations, setFilteredConversations] = useState([]);

    useEffect(() => {
        if (searchQuery.trim()) {
            const filtered = conversations.filter(conv => 
                conv.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                conv.username?.toLowerCase().includes(searchQuery.toLowerCase())
            );
            setFilteredConversations(filtered);
        } else {
            setFilteredConversations(conversations);
        }
    }, [conversations, searchQuery]);

    const formatUserName = (user) => {
        if (user.first_name || user.last_name) {
            return `${user.first_name || ''} ${user.last_name || ''}`.trim();
        }
        return user.username;
    };

    const getInitials = (name) => {
        return name
            .split(' ')
            .map(word => word[0])
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    return (
        <div className={`sidebar ${collapsed ? 'collapsed' : ''}`}>
            {/* Header */}
            <div className="sidebar-header">
                <div className="user-info" onClick={onShowProfile}>
                    <div className="user-avatar">
                        {user.avatar_url ? (
                            <img src={user.avatar_url} alt="Avatar" />
                        ) : (
                            <span className="avatar-initials">
                                {getInitials(formatUserName(user))}
                            </span>
                        )}
                    </div>
                    {!collapsed && (
                        <div className="user-details">
                            <div className="user-name">{formatUserName(user)}</div>
                            <div className="user-status">Online</div>
                        </div>
                    )}
                </div>
                
                <div className="header-actions">
                    {!collapsed && (
                        <>
                            <button 
                                className="action-btn"
                                title="New Chat"
                                onClick={() => {/* TODO: Implement new chat */}}
                            >
                                ✏️
                            </button>
                            <button 
                                className="action-btn"
                                title="Settings"
                                onClick={onShowProfile}
                            >
                                ⚙️
                            </button>
                        </>
                    )}
                    <button 
                        className="action-btn"
                        title={collapsed ? "Expand" : "Collapse"}
                        onClick={onToggleCollapse}
                    >
                        {collapsed ? '→' : '←'}
                    </button>
                </div>
            </div>

            {/* Search */}
            {!collapsed && (
                <div className="sidebar-search">
                    <SearchBar 
                        value={searchQuery}
                        onChange={setSearchQuery}
                        placeholder="Search conversations..."
                    />
                </div>
            )}

            {/* Conversations */}
            <div className="sidebar-content">
                {loading ? (
                    <div className="sidebar-loading">
                        <div className="loading-spinner small"></div>
                        {!collapsed && <span>Loading conversations...</span>}
                    </div>
                ) : (
                    <ConversationList 
                        conversations={filteredConversations}
                        collapsed={collapsed}
                    />
                )}
            </div>

            {/* Footer */}
            {!collapsed && (
                <div className="sidebar-footer">
                    <button 
                        className="logout-btn"
                        onClick={onLogout}
                        title="Logout"
                    >
                        <span className="logout-icon">🚪</span>
                        Logout
                    </button>
                </div>
            )}
        </div>
    );
};

export default Sidebar;
