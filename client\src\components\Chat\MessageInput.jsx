import React, { useState, useRef, useEffect } from 'react';
import './MessageInput.css';

const MessageInput = ({ onSendMessage, onTypingStart, onTypingStop, disabled }) => {
    const [message, setMessage] = useState('');
    const [isTyping, setIsTyping] = useState(false);
    const textareaRef = useRef(null);
    const typingTimeoutRef = useRef(null);

    useEffect(() => {
        // Auto-resize textarea
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
        }
    }, [message]);

    const handleInputChange = (e) => {
        const value = e.target.value;
        setMessage(value);

        // Handle typing indicators
        if (value.trim() && !isTyping) {
            setIsTyping(true);
            onTypingStart();
        }

        // Clear existing timeout
        if (typingTimeoutRef.current) {
            clearTimeout(typingTimeoutRef.current);
        }

        // Set new timeout to stop typing
        typingTimeoutRef.current = setTimeout(() => {
            if (isTyping) {
                setIsTyping(false);
                onTypingStop();
            }
        }, 1000);
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        }
    };

    const handleSend = () => {
        const trimmedMessage = message.trim();
        if (!trimmedMessage || disabled) return;

        onSendMessage(trimmedMessage);
        setMessage('');
        
        // Stop typing indicator
        if (isTyping) {
            setIsTyping(false);
            onTypingStop();
        }

        // Clear timeout
        if (typingTimeoutRef.current) {
            clearTimeout(typingTimeoutRef.current);
        }

        // Focus back to input
        textareaRef.current?.focus();
    };

    const handleEmojiClick = () => {
        // TODO: Implement emoji picker
        console.log('Emoji picker not implemented yet');
    };

    const handleAttachClick = () => {
        // TODO: Implement file attachment
        console.log('File attachment not implemented yet');
    };

    return (
        <div className="message-input-container">
            <div className="message-input">
                <button 
                    className="input-action-btn"
                    onClick={handleAttachClick}
                    title="Attach file"
                    disabled={disabled}
                >
                    📎
                </button>

                <div className="input-field">
                    <textarea
                        ref={textareaRef}
                        value={message}
                        onChange={handleInputChange}
                        onKeyPress={handleKeyPress}
                        placeholder="Type a message..."
                        disabled={disabled}
                        rows={1}
                        className="message-textarea"
                    />
                </div>

                <button 
                    className="input-action-btn"
                    onClick={handleEmojiClick}
                    title="Add emoji"
                    disabled={disabled}
                >
                    😊
                </button>

                <button 
                    className={`send-btn ${message.trim() ? 'active' : ''}`}
                    onClick={handleSend}
                    disabled={!message.trim() || disabled}
                    title="Send message"
                >
                    {disabled ? '⏳' : '➤'}
                </button>
            </div>
        </div>
    );
};

export default MessageInput;
